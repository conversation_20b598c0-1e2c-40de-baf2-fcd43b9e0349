<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scroll Position Test</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            background: #f8f9fa;
            border-right: 1px solid #dee2e6;
            transition: width 0.3s ease;
            position: relative;
        }
        
        .sidebar.collapsed {
            width: 64px;
        }
        
        .sidebar.expanded {
            width: 256px;
        }
        
        .scroll-container {
            height: 100%;
            overflow-y: auto;
            padding: 16px;
        }
        
        .scroll-container.hidden {
            display: none;
        }
        
        .item {
            padding: 12px;
            margin: 8px 0;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        
        .controls {
            position: fixed;
            top: 16px;
            right: 16px;
            z-index: 1000;
        }
        
        button {
            padding: 8px 16px;
            margin: 0 4px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .debug {
            position: fixed;
            bottom: 16px;
            right: 16px;
            background: #343a40;
            color: white;
            padding: 16px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar expanded" id="sidebar">
            <!-- Collapsed View -->
            <div class="scroll-container hidden" id="collapsedContainer">
                <h3>Collapsed View</h3>
                <div class="item">Icon 1</div>
                <div class="item">Icon 2</div>
                <div class="item">Icon 3</div>
                <div class="item">Icon 4</div>
                <div class="item">Icon 5</div>
                <div class="item">Icon 6</div>
                <div class="item">Icon 7</div>
                <div class="item">Icon 8</div>
                <div class="item">Icon 9</div>
                <div class="item">Icon 10</div>
                <div class="item">Icon 11</div>
                <div class="item">Icon 12</div>
                <div class="item">Icon 13</div>
                <div class="item">Icon 14</div>
                <div class="item">Icon 15</div>
            </div>
            
            <!-- Expanded View -->
            <div class="scroll-container" id="expandedContainer">
                <h3>Expanded View</h3>
                <div class="item">Dashboard</div>
                <div class="item">Users Management</div>
                <div class="item">All Users</div>
                <div class="item">Customers</div>
                <div class="item">Vendors</div>
                <div class="item">Drivers</div>
                <div class="item">Operations</div>
                <div class="item">Orders</div>
                <div class="item">Disputes</div>
                <div class="item">Notifications</div>
                <div class="item">Reviews</div>
                <div class="item">Support Chat</div>
                <div class="item">Content Management</div>
                <div class="item">CMS Dashboard</div>
                <div class="item">Media Library</div>
                <div class="item">Promotions</div>
                <div class="item">Banners</div>
                <div class="item">Marketing</div>
                <div class="item">Campaigns</div>
                <div class="item">Coupons</div>
                <div class="item">Loyalty Program</div>
                <div class="item">Push Notifications</div>
                <div class="item">Analytics & Reports</div>
                <div class="item">Overview Analytics</div>
                <div class="item">Sales Reports</div>
                <div class="item">User Reports</div>
                <div class="item">Performance</div>
                <div class="item">Custom Reports</div>
                <div class="item">Financial</div>
                <div class="item">Payouts</div>
                <div class="item">Transactions</div>
                <div class="item">Revenue</div>
                <div class="item">Commissions</div>
                <div class="item">Logistics</div>
                <div class="item">Delivery Zones</div>
                <div class="item">Delivery Times</div>
                <div class="item">Fleet Management</div>
                <div class="item">System</div>
                <div class="item">General Settings</div>
                <div class="item">API Configuration</div>
                <div class="item">Security</div>
                <div class="item">Admin Accounts</div>
                <div class="item">Access Keys</div>
            </div>
        </div>
        
        <div style="flex: 1; padding: 20px;">
            <h1>Scroll Position Preservation Test</h1>
            <p>This test simulates the admin sidebar scroll position preservation.</p>
            <ol>
                <li>Scroll down in the expanded sidebar (left panel)</li>
                <li>Click "Collapse" to switch to collapsed view</li>
                <li>Scroll in the collapsed view</li>
                <li>Click "Expand" to switch back</li>
                <li>Verify that both views maintain their scroll positions</li>
            </ol>
        </div>
    </div>
    
    <div class="controls">
        <button onclick="toggleSidebar()">Toggle Sidebar</button>
        <button onclick="clearStorage()">Clear Storage</button>
    </div>
    
    <div class="debug" id="debug">
        Debug info will appear here...
    </div>

    <script>
        let isCollapsed = false;
        const sidebar = document.getElementById('sidebar');
        const expandedContainer = document.getElementById('expandedContainer');
        const collapsedContainer = document.getElementById('collapsedContainer');
        const debug = document.getElementById('debug');
        
        // Throttle object for scroll saving
        const throttle = { expanded: null, collapsed: null };
        
        function log(message) {
            console.log(message);
            debug.innerHTML = message + '<br>' + debug.innerHTML;
            // Keep only last 10 lines
            const lines = debug.innerHTML.split('<br>');
            if (lines.length > 10) {
                debug.innerHTML = lines.slice(0, 10).join('<br>');
            }
        }
        
        function saveScrollPosition(container, key) {
            if (throttle[key]) {
                clearTimeout(throttle[key]);
            }
            
            throttle[key] = setTimeout(() => {
                sessionStorage.setItem(key, container.scrollTop.toString());
                log(`💾 Saved ${key}: ${container.scrollTop}`);
            }, 100);
        }
        
        function restoreScrollPosition(container, key) {
            const saved = sessionStorage.getItem(key);
            if (saved) {
                const position = parseInt(saved, 10);
                if (!isNaN(position) && position >= 0) {
                    container.scrollTop = position;
                    log(`🔄 Restored ${key}: ${position}`);
                }
            }
        }
        
        function toggleSidebar() {
            log(`🔄 Toggling to: ${isCollapsed ? 'expanded' : 'collapsed'}`);
            
            if (isCollapsed) {
                // Save collapsed position immediately
                const currentScroll = collapsedContainer.scrollTop;
                sessionStorage.setItem('collapsed-scroll', currentScroll.toString());
                log(`💾 IMMEDIATE: Saved collapsed: ${currentScroll}`);
                
                // Switch to expanded
                sidebar.className = 'sidebar expanded';
                collapsedContainer.className = 'scroll-container hidden';
                expandedContainer.className = 'scroll-container';
                
                // Restore expanded position
                restoreScrollPosition(expandedContainer, 'expanded-scroll');
                isCollapsed = false;
            } else {
                // Save expanded position immediately
                const currentScroll = expandedContainer.scrollTop;
                sessionStorage.setItem('expanded-scroll', currentScroll.toString());
                log(`💾 IMMEDIATE: Saved expanded: ${currentScroll}`);
                
                // Switch to collapsed
                sidebar.className = 'sidebar collapsed';
                expandedContainer.className = 'scroll-container hidden';
                collapsedContainer.className = 'scroll-container';
                
                // Restore collapsed position
                restoreScrollPosition(collapsedContainer, 'collapsed-scroll');
                isCollapsed = true;
            }
        }
        
        function clearStorage() {
            sessionStorage.removeItem('expanded-scroll');
            sessionStorage.removeItem('collapsed-scroll');
            log('🗑️ Cleared storage');
        }
        
        // Add scroll listeners
        expandedContainer.addEventListener('scroll', () => {
            saveScrollPosition(expandedContainer, 'expanded-scroll');
        });
        
        collapsedContainer.addEventListener('scroll', () => {
            saveScrollPosition(collapsedContainer, 'collapsed-scroll');
        });
        
        // Initial load
        log('🚀 Test loaded - try scrolling and toggling!');
        restoreScrollPosition(expandedContainer, 'expanded-scroll');
    </script>
</body>
</html>
