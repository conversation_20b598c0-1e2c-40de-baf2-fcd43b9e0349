# Admin Sidebar Scroll Preservation Test Plan

## Test Instructions

### Prerequisites
1. Open the admin panel: `http://localhost:3001/admin`
2. Open browser developer console to see debug logs
3. Ensure the sidebar has enough content to scroll (it should with all the navigation items)

### Test Case 1: Basic Collapse/Expand Preservation
**Steps:**
1. Ensure sidebar is in expanded state
2. Scroll down in the expanded sidebar (you should see navigation categories like "User Management", "Operations", etc.)
3. Note the scroll position
4. Click the collapse button (logo/brand area in header)
5. Sidebar should collapse to icon-only view
6. Click expand button again
7. **Expected Result**: Sidebar should restore to the exact scroll position from step 3

**Debug Output**: Look for console logs like:
- `💾 Saved expanded scroll position: X`
- `🔄 Restored expanded scroll position: X`

### Test Case 2: Independent Scroll Positions
**Steps:**
1. Start with expanded sidebar
2. Scroll to middle of expanded sidebar
3. Collapse sidebar
4. Scroll in collapsed sidebar (if it has scroll)
5. Expand sidebar again
6. **Expected Result**: Should return to middle position from step 2
7. Collapse again
8. **Expected Result**: Should return to collapsed scroll position from step 4

### Test Case 3: Page Refresh Preservation
**Steps:**
1. Scroll to a specific position in either expanded or collapsed view
2. Refresh the page (F5 or Ctrl+R)
3. **Expected Result**: After page loads, sidebar should restore to the same scroll position

### Test Case 4: Tab Switching Preservation
**Steps:**
1. Scroll to a specific position
2. Switch to another browser tab
3. Switch back to the admin panel tab
4. **Expected Result**: Scroll position should be preserved

### Test Case 5: Browser Navigation Preservation
**Steps:**
1. Scroll to a specific position
2. Navigate to another admin page (e.g., click on "Users" or "Orders")
3. Use browser back button to return
4. **Expected Result**: Scroll position should be preserved

### Test Case 6: Mobile Responsiveness
**Steps:**
1. Resize browser to mobile width (< 1024px)
2. Open sidebar using hamburger menu
3. Scroll in sidebar
4. Close sidebar
5. Open sidebar again
6. **Expected Result**: Scroll position should be preserved

## Debugging

### Console Logs to Watch For:
- `💾 Saved expanded scroll position: [number]`
- `💾 Saved collapsed scroll position: [number]`
- `🔄 Restored expanded scroll position: [number]`
- `🔄 Restored collapsed scroll position: [number]`

### SessionStorage Inspection:
Open browser DevTools → Application → Storage → Session Storage → localhost:3001
Look for keys:
- `admin-sidebar-expanded-scroll`
- `admin-sidebar-collapsed-scroll`

### Common Issues to Check:
1. **No scroll restoration**: Check if sessionStorage keys exist
2. **Wrong position**: Verify the saved values match expected positions
3. **Performance issues**: Monitor console for excessive logging
4. **Memory leaks**: Check if timeouts are properly cleared

## Expected Behavior Summary

✅ **Working Correctly When:**
- Scroll positions are preserved across collapse/expand
- Each view (collapsed/expanded) maintains independent scroll positions
- Positions survive page refreshes and navigation
- No console errors related to scroll preservation
- Smooth user experience without scroll jumps

❌ **Issues to Report:**
- Scroll position resets to top after collapse/expand
- Scroll positions interfere with each other
- Console errors related to sessionStorage
- Performance degradation during scrolling
- Memory leaks (check DevTools Memory tab)

## Performance Verification

1. **Throttling Check**: Scroll rapidly and verify logs don't spam console
2. **Memory Check**: Use DevTools Memory tab to ensure no memory leaks
3. **Storage Check**: Verify sessionStorage isn't growing excessively

## Browser Testing

Test in multiple browsers:
- ✅ Chrome/Chromium
- ✅ Firefox
- ✅ Safari
- ✅ Edge

## Clean Up

After testing, you can remove the debug console.log statements from the production code by removing lines containing:
- `console.log('💾 Saved expanded scroll position:', ...)`
- `console.log('💾 Saved collapsed scroll position:', ...)`
- `console.log('🔄 Restored expanded scroll position:', ...)`
- `console.log('🔄 Restored collapsed scroll position:', ...)`
